import { Enti<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GameMode, Player, system } from "@minecraft/server";
import { getDistance } from "../../../utilities/vector3";
import { PIGLIN_CHAMPION_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
/**
 * Attack timing in ticks - when the damage should be applied during the animation
 */
const ATTACK_TIMING = 38;
/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 104;
/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;
/**
 * Executes the horizontal attack for the Piglin Champion using the new timing system
 * Uses localized runTimeout for attack timing, reset, and cooldown
 *
 * @param piglinChampion The piglin champion entity
 */
export function executeHorizontalAttack(piglinChampion) {
    // Wait for the attack timing before executing the attack
    let timing = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_bb:dead");
            if (isDead) {
                system.clearRun(timing);
                return;
            }
            if (piglinChampion.getProperty("ptd_bb:attack") === "horizontal") {
                performHorizontalAttack(piglinChampion);
            }
        }
        catch (error) {
            system.clearRun(timing);
        }
    }, ATTACK_TIMING);
    // Reset the attack state to "none" after the animation is complete
    let reset = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_bb:dead");
            const currentAttack = piglinChampion.getProperty("ptd_bb:attack");
            if (isDead) {
                system.clearRun(reset);
                return;
            }
            // Only reset if the attack is still "horizontal" - prevents interference with stuns
            if (currentAttack === "horizontal") {
                piglinChampion.triggerEvent("ptd_bb:reset_attack");
            }
            system.clearRun(reset);
        }
        catch (error) {
            system.clearRun(reset);
        }
    }, ANIMATION_TIME);
    // Wait for cooldown, then set cooldown property to false to select the next attack
    let cooldown = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_bb:dead");
            if (isDead) {
                system.clearRun(cooldown);
                return;
            }
            piglinChampion.setProperty("ptd_bb:cooling_down", false);
            system.clearRun(cooldown);
        }
        catch (error) {
            system.clearRun(cooldown);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
    return;
}
/**
 * Performs the actual horizontal attack damage and effects
 * @param piglinChampion The piglin champion entity
 */
function performHorizontalAttack(piglinChampion) {
    // Apply damage to nearby entities
    const damageRadius = 4;
    // Use direct damage value instead of percentage
    const damage = PIGLIN_CHAMPION_ATTACK_DAMAGES.horizontal.damage;
    // Get direction vector directly from the piglin's view direction
    const viewDirection = piglinChampion.getViewDirection();
    const dirX = viewDirection.x;
    const dirZ = viewDirection.z;
    // Calculate position 7 blocks in front of the piglin
    const originPos = {
        x: piglinChampion.location.x + dirX * 7,
        y: piglinChampion.location.y,
        z: piglinChampion.location.z + dirZ * 7
    };
    piglinChampion.dimension
        .getEntities({
        location: originPos,
        maxDistance: damageRadius,
        excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
        excludeFamilies: ["piglin_champion", "piglin", "rock"]
    })
        .forEach((entity) => {
        // Use piglin's direction for knockback
        // Create 2D points (same y-coordinate) to calculate horizontal distance
        const point1 = { x: entity.location.x, y: 0, z: entity.location.z };
        const point2 = { x: originPos.x, y: 0, z: originPos.z };
        const distance = getDistance(point1, point2);
        if (distance > 0) {
            // Use the piglin's direction for knockback
            const nx = dirX;
            const nz = dirZ;
            // Horizontal attack parameters
            const horizontalStrength = 7.0; // Increased to knock players back 8 blocks
            const verticalStrength = 0.8;
            try {
                // Try to apply knockback first
                if (entity instanceof Player) {
                    const gameMode = entity.getGameMode();
                    if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                        entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                    }
                }
                else {
                    entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                }
            }
            catch (e) {
                // Fallback to applyImpulse if applyKnockback fails
                const impulse = {
                    x: nx * horizontalStrength,
                    y: verticalStrength,
                    z: nz * horizontalStrength
                };
                entity.applyImpulse(impulse);
            }
        }
        // Apply damage after knockback/impulse
        entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinChampion });
    });
}
