import { system, Entity, Vector3, EntityDamageCause } from "@minecraft/server";
import { filterValidTargets } from "../../general_mechanics/targetUtils";
import { GRIMHOWL_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";

export function doGrimhowlSlash(sourceEntity: Entity): void {
    try {
        if (!sourceEntity) return;
        
        const isEnraged: boolean = sourceEntity.getProperty('ptd_bb:enraged') as boolean;
        sourceEntity.triggerEvent(isEnraged ? 'ptd_bb:grimhowl_slash_enraged' : 'ptd_bb:grimhowl_slash');
        
        const nearbyTargets: Entity[] = sourceEntity.dimension.getEntities({
            location: sourceEntity.location,
            excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
            maxDistance: 16
        }).filter(filterValidTargets(sourceEntity));
    
        if (nearbyTargets.length === 0) {
            sourceEntity.triggerEvent('ptd_bb:attack_done');
            return;
        }
    
        sourceEntity.teleport(sourceEntity.location, { facingLocation: (nearbyTargets[0] as Entity).location, keepVelocity: true });
        sourceEntity.runCommand(`playsound mob.ptd_bb_grimhowl.grunt @a ~ ~ ~ 5${1 + (Math.random() * 0.3)}`);
    
        system.runTimeout(() => {
            if (!sourceEntity) return;
        
            const viewDirection: Vector3 = sourceEntity.getViewDirection();
            const impulse: Vector3 = {
                x: viewDirection.x * (sourceEntity.getProperty('ptd_bb:sword_mode') ? 2.5 : 4),
                y: 0.1,
                z: viewDirection.z * (sourceEntity.getProperty('ptd_bb:sword_mode') ? 2.5 : 4)
            };
            sourceEntity.applyImpulse(impulse);
        
            const collideTargets: Entity[] = sourceEntity.dimension.getEntities({
                location: sourceEntity.location,
                excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
                maxDistance: 4.4
            }).filter(filterValidTargets(sourceEntity));
        
            collideTargets.forEach((entity: Entity) => {
                entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.collision_damage.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
            });
        }, isEnraged ? 5 : 10);
    
        system.runTimeout(() => {
            if (!sourceEntity) return;
        
            if (sourceEntity.getProperty('ptd_bb:sword_mode')) {
                sourceEntity.runCommand(`playsound mob.ptd_bb_grimhowl.sword_swipe @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
            } else {
                sourceEntity.runCommand(`playsound mob.ptd_bb_grimhowl.grunt @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
                sourceEntity.runCommand(`playsound mob.ravager.bite @a ~ ~ ~ 10 ${2 + (Math.random() * 0.3)}`);
            }
            const viewDirection: Vector3 = sourceEntity.getViewDirection();
            const forwardLocation: Vector3 = {
                x: sourceEntity.location.x + viewDirection.x * 5,
                y: sourceEntity.location.y,
                z: sourceEntity.location.z + viewDirection.z * 5
            };
        
            const collideTargets: Entity[] = sourceEntity.dimension.getEntities({
                location: sourceEntity.location,
                excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
                maxDistance: 4.4
            }).filter(filterValidTargets(sourceEntity));
        
            const frontTargets: Entity[] = sourceEntity.dimension.getEntities({
                location: forwardLocation,
                excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
                maxDistance: (sourceEntity.getProperty('ptd_bb:sword_mode') ? 5 : 2.5)
            }).filter(filterValidTargets(sourceEntity));
        
            collideTargets.forEach((entity: Entity) => {
                entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.collision_damage.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
            });
        
            frontTargets.forEach((entity: Entity) => {
                entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.slash.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
            });
        }, isEnraged ? 10 : 20);
    } catch (error) {
        sourceEntity?.triggerEvent('ptd_bb:attack_done');
    }
    
}
