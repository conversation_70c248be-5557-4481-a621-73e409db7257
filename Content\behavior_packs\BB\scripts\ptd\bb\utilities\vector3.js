/**
 * Generates a random location within a specified range around a given point, with optional air block validation.
 *
 * @param location - The base Vector3 location to offset from
 * @param dimension - The Minecraft dimension to check blocks in
 * @param baseOffset - The minimum distance from the original location
 * @param additionalOffset - Extra distance added to the base offset
 * @param randomYOffset - Vertical offset applied to the Y coordinate
 * @param checkForAirBlock - If true, ensures the returned location contains an air block
 *
 * @returns A new Vector3 location with random offsets applied, or undefined if no valid location was found after 30 attempts
 * when checkForAirBlock is true
 *
 * @remarks
 * - The X and Z coordinates are now generated independently for better spatial distribution
 * - The total offset range for each axis is -totalOffset to +totalOffset where totalOffset = baseOffset + additionalOffset
 * - The function will attempt up to 30 times to find a location with an air block if checkForAirBlock is true
 * - The Y coordinate is only offset by the fixed randomYOffset value, not randomized
 */
export function getRandomLocation(location, dimension, baseOffset, additionalOffset, randomYOffset, checkForAirBlock) {
    // Calculate a random offset within the total range for a single coordinate
    const randomOffset = () => {
        const totalOffset = baseOffset + additionalOffset;
        return Math.random() * (totalOffset * 2) - totalOffset;
    };
    // Create a new location with random offsets applied to all axes
    const generateRandomLocation = () => ({
        x: location.x + randomOffset(),
        y: location.y + randomYOffset,
        z: location.z + randomOffset()
    });
    // Verify if the target spawn location contains an air block
    const isAirBlock = (location) => {
        const block = dimension.getBlock(location);
        return block?.isAir ?? false;
    };
    // Generate initial random location
    let randomLocation = generateRandomLocation();
    // If we don't need to check for air blocks, return the first generated location
    if (!checkForAirBlock) {
        return randomLocation;
    }
    // Try up to 20 times to find a valid spawn location if air check is required
    let attempts = 0;
    while (!isAirBlock(randomLocation) && attempts < 30) {
        randomLocation = generateRandomLocation();
        attempts++;
    }
    // Only return the location if a valid spot was found within the attempt limit
    return attempts < 30 ? randomLocation : undefined;
}
/**
 * Calculates the distance between two Vector3 points
 *
 * @param point1 The first point
 * @param point2 The second point
 * @returns The distance between the two points
 */
export function getDistance(point1, point2) {
    const dx = point1.x - point2.x;
    const dy = point1.y - point2.y;
    const dz = point1.z - point2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
}
/**
 * Gets the direction vector from one point to another
 *
 * @param from Starting position
 * @param to Ending position
 * @returns Normalized direction vector
 */
export function getDirection(from, to) {
    const dx = to.x - from.x;
    const dy = to.y - from.y;
    const dz = to.z - from.z;
    // Calculate the length of the vector
    const length = Math.sqrt(dx * dx + dy * dy + dz * dz);
    // Normalize the vector
    if (length > 0) {
        return {
            x: dx / length,
            y: dy / length,
            z: dz / length
        };
    }
    else {
        // Return a default vector if points are the same
        return { x: 0, y: 0, z: 0 };
    }
}
/**
 * Creates a Vector3 from individual components
 *
 * @param x X coordinate
 * @param y Y coordinate
 * @param z Z coordinate
 * @returns Vector3 object
 */
export function createVector3(x, y, z) {
    return { x, y, z };
}
