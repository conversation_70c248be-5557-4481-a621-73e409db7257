import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GameMode, Player, system, Vector3 } from "@minecraft/server";
import { getTarget } from "../../../../general_mechanics/targetUtils";
import { getDistance } from "../../../../../utilities/vector3";

/**
 * Attack timing in ticks - when the damage should be applied during the animation
 * Based on the slam animation, damage should occur around 50% through (around tick 15)
 */
const ATTACK_TIMING = 15;

/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 30; // 1.5 seconds

/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20; // 1 second

/**
 * Normalizes a 2D direction vector
 * @param x X component
 * @param z Z component
 * @returns Normalized direction vector
 */
function normalizeDirection(x: number, z: number): { x: number; z: number } {
  const length = Math.sqrt(x * x + z * z);
  if (length > 0) {
    return { x: x / length, z: z / length };
  }
  return { x: 0, z: 0 };
}

/**
 * Executes the slam attack for the Piglin Marauder
 * Creates a powerful ground slam that damages and knocks back entities in a large area
 *
 * @param piglinMarauder The piglin marauder entity
 */
export function executeSlamAttack(piglinMarauder: Entity): void {
  // Attack parameters
  const damageRadius = 4; // Larger radius than zombie brute
  const damage = 12; // Higher damage than zombie brute

  // Wait for the attack timing before executing the attack
  let timing = system.runTimeout(() => {
    try {
      const isDead = piglinMarauder.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(timing);
        return;
      }

      // Get the marauder's current target for direction calculation
      const target = getTarget(piglinMarauder, piglinMarauder.location, 32, ["piglin_champion", "piglin", "rock"]);
      let dirX = 0;
      let dirZ = 1; // Default forward direction

      if (target) {
        const dx = target.location.x - piglinMarauder.location.x;
        const dz = target.location.z - piglinMarauder.location.z;
        const normalized = normalizeDirection(dx, dz);
        dirX = normalized.x;
        dirZ = normalized.z;
      }

      // Calculate position 1.5 blocks in front of the marauder in the direction of the target
      const originPos: Vector3 = {
        x: piglinMarauder.location.x + dirX * 1.5,
        y: piglinMarauder.location.y,
        z: piglinMarauder.location.z + dirZ * 1.5
      };

      // Play attack impact sound and particles
      piglinMarauder.dimension.spawnParticle("ptd_bb:pg_foot_stomp1_01", originPos);
      piglinMarauder.dimension.playSound("mob.piglin.angry", originPos, { volume: 8.0, pitch: 0.5 });
      piglinMarauder.dimension.playSound("random.explode", originPos, { volume: 1.5, pitch: 0.8 });

      // Find entities within the damage radius
      piglinMarauder.dimension
        .getEntities({
          location: originPos,
          maxDistance: damageRadius,
          excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
          excludeFamilies: ["piglin_champion", "piglin", "rock"]
        })
        .forEach((entity: Entity) => {
          // Calculate distance for knockback
          const point1: Vector3 = { x: entity.location.x, y: 0, z: entity.location.z };
          const point2: Vector3 = { x: originPos.x, y: 0, z: originPos.z };
          const distance = getDistance(point1, point2);

          if (distance > 0) {
            // Use the direction for knockback
            const nx = dirX;
            const nz = dirZ;

            // Slam attack parameters - stronger than zombie brute
            const horizontalStrength = 3.0;
            const verticalStrength = 1.0;

            try {
              // Try to apply knockback first
              if (entity instanceof Player) {
                const gameMode = entity.getGameMode();
                if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                  entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                }
              } else {
                entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
              }
            } catch (e) {
              // Fallback to applyImpulse if applyKnockback fails
              const impulse: Vector3 = {
                x: nx * horizontalStrength,
                y: verticalStrength,
                z: nz * horizontalStrength
              };

              entity.applyImpulse(impulse);
            }
          }

          // Apply damage after knockback/impulse
          entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinMarauder });
        });

      // Clear the timeout
      system.clearRun(timing);
    } catch (error) {
      // handle error silently
      system.clearRun(timing);
    }
  }, ATTACK_TIMING);

  // Reset the attack state to "none" after the animation is complete
  let reset = system.runTimeout(() => {
    try {
      const isDead = piglinMarauder.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(reset);
        return;
      }
      piglinMarauder.triggerEvent("ptd_bb:reset_attack");
      system.clearRun(reset);
    } catch (error) {
      // handle error silently
      system.clearRun(reset);
    }
  }, ANIMATION_TIME);

  // Wait for cooldown, then set cooldown property to false to select the next attack
  let cooldown = system.runTimeout(() => {
    try {
      const isDead = piglinMarauder.getProperty("ptd_bb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldown);
        return;
      }
      piglinMarauder.setProperty("ptd_bb:cooling_down", false);
      system.clearRun(cooldown);
    } catch (error) {
      // handle error silently
      system.clearRun(cooldown);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);

  return;
}
