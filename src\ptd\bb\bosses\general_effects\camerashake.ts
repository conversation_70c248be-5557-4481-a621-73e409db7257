import { Entity, Vector3 } from "@minecraft/server";
import { getDistance } from "../../utilities/vector3";


export function handleCameraShakeScriptEvent(entity: Entity): void {
    const entityTypeId = entity.typeId;

    // Define default parameters
    let radius;
    let minStrength;;
    let maxStrength;
    let length;

    // Adjust parameters based on entity type
    switch (entityTypeId) {
        case 'ptd_bb:piglin_champion':
            // Stronger shake for piglin champion
            radius = 32;
            minStrength = 0.01;
            maxStrength = 0.5;
            length = 0.2;
            break;
        // Add more entity types here with their specific parameters
        default:
            // Default parameters for other entities
            radius = 16;
            minStrength = 0.05;
            maxStrength = 0.3;
            length = 0.1;
            break;
    }
    cameraShake(entity, radius, minStrength, maxStrength, length);
}

/**
 * Creates a camera shake effect for players within a radius
 * @param source The entity or location causing the camera shake
 * @param radius The radius within which players will experience the camera shake
 * @param minStrength The minimum strength of the camera shake (for players at the edge of the radius)
 * @param maxStrength The maximum strength of the camera shake (for players closest to the source)
 * @param length The duration of the camera shake effect
 */
export function cameraShake(
    source: Entity | Vector3,
    radius: number,
    minStrength: number,
    maxStrength: number,
    length: number
): void {
    // Get the source location (either from an entity or directly from a Vector3)
    const sourceLocation: Vector3 = 'location' in source ? source.location : source;

    // Get the dimension (if source is an entity)
    const dimension = 'dimension' in source ? source.dimension : undefined;

    if (!dimension) {
        console.warn("Camera shake source must be an entity with a dimension");
        return;
    }

    // Get all players within the radius
    const players = dimension.getPlayers({
        location: sourceLocation,
        maxDistance: radius
    });

    // Apply camera shake to each player
    players.forEach(player => {
        // Only apply to players on the ground
        if (!player.isOnGround) return;

        // Calculate distance from source
        const distance = getDistance(sourceLocation, player.location);

        // Skip if outside radius (should be handled by getPlayers, but just in case)
        if (distance > radius) return;

        // Calculate strength based on distance (inversely proportional)
        // Players closer to the source get stronger shake
        const strengthMultiplier = 1 - (distance / radius); // 0 at edge, 1 at center
        const strength = minStrength + strengthMultiplier * (maxStrength - minStrength);

        // Apply camera shake using /camerashake command with player's name
        player.runCommand(`camerashake add @s ${strength.toFixed(2)} ${length.toFixed(2)} positional`);
    });
}
