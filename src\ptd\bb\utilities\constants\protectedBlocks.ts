/**
 * Set of block IDs that are protected from being modified by certain events
 */
export const PROTECTED_BLOCKS = new Set([
  // Critical blocks
  'minecraft:bedrock',
  'minecraft:barrier',
  'minecraft:command_block',
  'minecraft:structure_block',
  'minecraft:end_portal_frame',
  'minecraft:end_portal',
  'minecraft:end_gateway',
  'minecraft:portal',

  // Storage blocks
  'minecraft:chest',
  'minecraft:trapped_chest',
  'minecraft:ender_chest',
  'minecraft:barrel',
  // All shulker box variants
  'minecraft:undyed_shulker_box',
  'minecraft:white_shulker_box',
  'minecraft:orange_shulker_box',
  'minecraft:magenta_shulker_box',
  'minecraft:light_blue_shulker_box',
  'minecraft:yellow_shulker_box',
  'minecraft:lime_shulker_box',
  'minecraft:pink_shulker_box',
  'minecraft:gray_shulker_box',
  'minecraft:light_gray_shulker_box',
  'minecraft:cyan_shulker_box',
  'minecraft:purple_shulker_box',
  'minecraft:blue_shulker_box',
  'minecraft:brown_shulker_box',
  'minecraft:green_shulker_box',
  'minecraft:red_shulker_box',
  'minecraft:black_shulker_box',

  // Valuable blocks
  'minecraft:diamond_block',
  'minecraft:emerald_block',
  'minecraft:gold_block',
  'minecraft:netherite_block',

  // Functional blocks
  'minecraft:furnace',
  'minecraft:blast_furnace',
  'minecraft:smoker',
  'minecraft:brewing_stand',
  'minecraft:enchanting_table',
  'minecraft:anvil',
  'minecraft:beacon',
  'minecraft:lodestone',
  'minecraft:respawn_anchor',
  'minecraft:lectern',
  'minecraft:cartography_table',
  'minecraft:smithing_table',
  'minecraft:grindstone',
  'minecraft:stonecutter',
  'minecraft:loom',
  'minecraft:composter',
  'minecraft:bell',

  // Redstone components
  'minecraft:hopper',
  'minecraft:dropper',
  'minecraft:dispenser',
  'minecraft:observer',
  'minecraft:comparator',
  'minecraft:repeater',
  'minecraft:daylight_detector',
  'minecraft:note_block',
  'minecraft:jukebox',
  'minecraft:piston',
  'minecraft:sticky_piston',
  'minecraft:redstone_lamp'
]);
