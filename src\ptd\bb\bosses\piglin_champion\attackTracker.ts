import { Entity } from "@minecraft/server";

// Types for attack history and configuration
type AttackHistory = Record<string, number>;
type AttackConfiguration = Record<string, number>;

// Available attacks for each range
export const SHORT_RANGE_ATTACKS = ["horizontal", "vertical", "foot_stomp", "spin_slam", "body_slam", "summoning_chant"];
export const MEDIUM_RANGE_ATTACKS = ["horizontal", "vertical", "foot_stomp", "spin_slam", "summoning_chant"];
export const LONG_RANGE_ATTACKS = ["horizontal", "vertical", "summoning_chant"];

// Attack usage configuration - defines how many times each attack can be used per cycle
export const ATTACK_USAGE_CONFIG: AttackConfiguration = {
  horizontal: 2,
  vertical: 2,
  foot_stomp: 2,
  spin_slam: 2,
  body_slam: 2,
  summoning_chant: 5
};

// Dynamic property name for global attack history
const GLOBAL_ATTACK_HISTORY_PROP = "globalAttackHistory";

/**
 * Initialize global attack history
 * @param entity The entity to initialize attack history for
 * @param attackConfig The attack configuration defining usage limits
 */
function initializeGlobalAttackHistory(entity: Entity, attackConfig: AttackConfiguration): void {
  const history: AttackHistory = {};
  for (const attack of Object.keys(attackConfig)) {
    history[attack] = 0;
  }

  // Set the dynamic property
  entity.setDynamicProperty(GLOBAL_ATTACK_HISTORY_PROP, JSON.stringify(history));
}

/**
 * Get available attacks from a range based on global attack history
 * @param entity The entity to get available attacks for
 * @param attacks The available attacks for the current range
 * @param attackConfig The attack configuration defining usage limits
 * @returns The available attacks based on global usage history
 */
export function getAvailableAttacks(entity: Entity, attacks: string[], attackConfig: AttackConfiguration = ATTACK_USAGE_CONFIG): string[] {
  const historyJson = entity.getDynamicProperty(GLOBAL_ATTACK_HISTORY_PROP) as string;

  // Initialize attack history if it doesn't exist
  if (!historyJson) {
    initializeGlobalAttackHistory(entity, attackConfig);
    return [...attacks]; // Return a copy of the attacks array
  }

  const history: AttackHistory = JSON.parse(historyJson);

  // Check if all attacks in the current range have reached their usage limits
  const allAttacksAtLimit = attacks.every((attack) => {
    const maxUsage = attackConfig[attack] ?? 2; // Default to 2 if not configured
    return (history[attack] ?? 0) >= maxUsage;
  });

  if (allAttacksAtLimit) {
    // Reset history if all attacks have reached their limits
    initializeGlobalAttackHistory(entity, attackConfig);
    return [...attacks]; // Return a copy of the attacks array
  }

  // Check if any attack hasn't been used yet
  const unusedAttacks = attacks.filter((attack) => (history[attack] ?? 0) === 0);
  if (unusedAttacks.length > 0) {
    return unusedAttacks;
  }

  // If all attacks have been used at least once, return attacks that haven't reached their limits
  return attacks.filter((attack) => {
    const maxUsage = attackConfig[attack] ?? 2; // Default to 2 if not configured
    return (history[attack] ?? 0) < maxUsage;
  });
}

/**
 * Update global attack history after an attack is selected
 * @param entity The entity to update attack history for
 * @param attack The selected attack
 */
export function updateAttackHistory(entity: Entity, attack: string): void {
  const historyJson = entity.getDynamicProperty(GLOBAL_ATTACK_HISTORY_PROP) as string;
  if (!historyJson) {
    return;
  }

  const history: AttackHistory = JSON.parse(historyJson);
  history[attack] = (history[attack] ?? 0) + 1;

  entity.setDynamicProperty(GLOBAL_ATTACK_HISTORY_PROP, JSON.stringify(history));
}

/**
 * Get the global attack history
 * @param entity The entity to get attack history for
 * @returns The global attack history as a Record<string, number> or null if not found
 */
export function getAttackHistory(entity: Entity): AttackHistory | null {
  const historyJson = entity.getDynamicProperty(GLOBAL_ATTACK_HISTORY_PROP) as string;

  if (!historyJson) {
    return null;
  }

  return JSON.parse(historyJson) as AttackHistory;
}

/**
 * Reset global attack history to allow all attacks to be used again
 * @param entity The entity to reset attack history for
 * @param attackConfig The attack configuration defining usage limits
 */
export function resetAttackHistory(entity: Entity, attackConfig: AttackConfiguration = ATTACK_USAGE_CONFIG): void {
  initializeGlobalAttackHistory(entity, attackConfig);
}

/**
 * Display global attack history on the actionbar
 * @param entity The entity to get attack history for
 */
export function displayAttackHistory(entity: Entity): void {
  const history = getAttackHistory(entity);
  if (!history) {
    return;
  }
  // For now, we don't display the history, but this function can be expanded in the future
}
