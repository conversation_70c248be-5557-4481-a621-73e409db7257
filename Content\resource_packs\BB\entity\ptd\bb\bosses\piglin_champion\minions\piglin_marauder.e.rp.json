{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ptd_bb:piglin_marauder", "min_engine_version": "1.16.0", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/ptd/bb/entity/bosses/piglin_champion/minions/piglin_marauder"}, "geometry": {"default": "geometry.ptd_bb_piglin_marauder"}, "animations": {"look_at_target": "animation.common.look_at_target", "spawn": "animation.ptd_bb.piglin_maruarder.spawn", "idle": "animation.ptd_bb.piglin_maruarder.idle", "idle_2": "animation.ptd_bb.piglin_maruarder.idle_2", "idle_3": "animation.ptd_bb.piglin_maruarder.idle_3", "walk": "animation.ptd_bb.piglin_maruarder.walk", "run": "animation.ptd_bb.piglin_maruarder.run", "slam_attack": "animation.ptd_bb.piglin_maruarder.slam_attack", "sweep_attack": "animation.ptd_bb.piglin_maruarder.sweep_attack", "damaged": "animation.ptd_bb.piglin_maruarder.damaged", "death": "animation.ptd_bb.piglin_maruarder.death", "dead": "animation.ptd_bb.piglin_maruarder.dead", "general": "controller.animation.ptd_bb.piglin_marauder.general"}, "scripts": {"should_update_bones_and_effects_offscreen": true, "animate": ["general", {"look_at_target": "q.property('ptd_bb:spawning') == false && q.property('ptd_bb:dead') == false"}]}, "render_controllers": ["controller.render.ptd_bb.default"], "spawn_egg": {"base_color": "#a57892", "overlay_color": "#9b8227"}}}}