import { EffectTypes } from "@minecraft/server";
/**
 *
 * @param skull The flying skull entity
 * @returns
 */
export function handleFlyingSkullHit(event) {
    try {
        const { projectile: skull, tags } = event;
        if (!skull)
            return;
        // Find the arcane blast tag
        const arcaneBlastTag = tags.find(tag => tag.includes("ptd_bb:arcane_blast_projectile"));
        // Check if the tag is for an arcane blast projectile or a book of the damned projectile
        if (arcaneBlastTag) {
            // Create explosion with wither effect
            const witherEffect = EffectTypes.get('minecraft:wither');
            if (!witherEffect) {
                // Fallback to string-based approach
                const fallbackEffect = EffectTypes.get("minecraft:weakness");
                addEffect(event, fallbackEffect);
            }
            else {
                addEffect(event, witherEffect);
            }
            return;
        }
        // If no relevant tags, explode normally
        else {
            addEffect(event, undefined);
        }
    }
    catch (error) {
        console.warn(`Error in handleFlyingSkullHit: ${error}`);
        return;
    }
    return;
}
/**
 *
 * @param skull
 * @param effect
 * @returns
 */
function addEffect(event, effect) {
    try {
        const { projectile: skull, dimension, location } = event;
        if (!skull)
            return;
        // Create a more visible explosion
        const explosionRadius = 4;
        // Apply effect to nearby entities if provided
        if (effect) {
            const nearbyEntities = dimension.getEntities({
                location: location,
                maxDistance: explosionRadius,
                excludeTypes: ["minecraft:xp_orb", "minecraft:item", "ptd_bb:rock", "ptd_bb:flying_skull"],
                excludeFamilies: ["necromancer", "inanimate"]
            });
            // Apply effect to each entity
            nearbyEntities.forEach((entity) => {
                try {
                    entity.addEffect(effect, 200, { amplifier: 1, showParticles: true });
                }
                catch (effectError) {
                    console.warn(`Failed to apply effect to ${entity.typeId}: ${effectError}`);
                }
            });
        }
    }
    catch (error) {
        console.warn(`Error in addEffect: ${error}`);
        return;
    }
    return;
}
