import { Entity, system, Vector3, Dimension } from "@minecraft/server";
import { getRandomLocation, getDistance } from "../../../utilities/vector3";
import { teleport } from "../../../utilities/teleport";
import { getTarget } from "../../general_mechanics/targetUtils";

/**
 * Attack timing constants for phantom phase ability phases
 */
const TELEPORT_TIMING = 37; // Teleport at tick 37 (end of phase_start)

/**
 * Animation times in ticks
 */
const TOTAL_ANIMATION_TIME = 83; // Total duration of the ability

/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;

/**
 * Configuration for the phantom phase ability
 */
const PHANTOM_PHASE_CONFIG = {
    /** Minimum distance from target in blocks */
    MIN_DISTANCE: 12,
    /** Maximum distance from target in blocks */
    MAX_DISTANCE: 24,
    /** Minimum distance from necromancer's initial location in blocks */
    MIN_DISTANCE_FROM_SELF: 16,
    /** Maximum number of attempts to find a valid teleport location */
    MAX_ATTEMPTS: 64
};

/**
 * Executes the phantom phase ability for the Necromancer using the new timing system
 * Uses localized runTimeout for teleportation, reset, and cooldown
 *
 * @param necromancer The necromancer entity
 */
export function executePhantomPhaseAbility(necromancer: Entity): void {

    // Timedown to teleport
    let teleportTiming = system.runTimeout(() => {
            try {
                const isDead = necromancer.getProperty("ptd_bb:dead") as boolean;
                if (isDead) {
                    system.clearRun(teleportTiming);
                    return;
                }

                if (necromancer.getProperty("ptd_bb:attack") === "phantom_phase_start") {
                    performPhantomPhaseTeleport(necromancer);
                }
            } catch (error) {
                system.clearRun(teleportTiming);
                // Transition to phase_end on error
                necromancer.triggerEvent("ptd_bb:phantom_phase_end_attack");
            }
        }, TELEPORT_TIMING);

        // Reset attack and start cooldown after phase_end animation completes
        let resetTiming = system.runTimeout(() => {
            try {
                const isDead = necromancer.getProperty("ptd_bb:dead") as boolean;
                if (isDead) {
                    system.clearRun(resetTiming);
                    return;
                }

                if (necromancer.getProperty("ptd_bb:attack") === "phantom_phase_end") {
                    necromancer.triggerEvent("ptd_bb:reset_attack");
                }
            } catch (error) {
                system.clearRun(resetTiming);
            }
        }, TOTAL_ANIMATION_TIME);

        // End cooldown after cooldown time
        let cooldownTiming = system.runTimeout(() => {
            try {
                const isDead = necromancer.getProperty("ptd_bb:dead") as boolean;
                if (isDead) {
                    system.clearRun(cooldownTiming);
                    return;
                }

                necromancer.setProperty("ptd_bb:cooling_down", false);
            } catch (error) {
                system.clearRun(cooldownTiming);
            }
        }, TOTAL_ANIMATION_TIME + COOLDOWN_TIME);
    
}

/**
 * Performs the phantom phase teleportation logic
 * @param necromancer The necromancer entity
 */
async function performPhantomPhaseTeleport(necromancer: Entity): Promise<void> {
    try {
        // Get the target
        const target = getTarget(necromancer, necromancer.location, 32, ["necromancer"]);

        if (!target) {
            // Transition to phase_end if no target is found
            necromancer.triggerEvent("ptd_bb:phantom_phase_end_attack");
            return;
        }

        // Store necromancer's initial location
        const necromancerLocation = { ...necromancer.location };

        // Get target location
        const targetLocation = target.location;

        // Try to find a valid teleport location
        let validLocation: Vector3 | undefined;
        let attempts = 0;

        while (!validLocation && attempts < PHANTOM_PHASE_CONFIG.MAX_ATTEMPTS) {
            attempts++;

            // Find a random location within the specified range from target
            const potentialLocation = getRandomLocation(
                targetLocation,
                necromancer.dimension,
                PHANTOM_PHASE_CONFIG.MIN_DISTANCE,  // Base offset (minimum distance from target)
                PHANTOM_PHASE_CONFIG.MAX_DISTANCE - PHANTOM_PHASE_CONFIG.MIN_DISTANCE,  // Additional offset
                0,  // No Y offset initially
                true  // Check for air block
            );

            if (!potentialLocation) continue;

            // Check if the location is far enough from the necromancer's initial position
            const distanceFromSelf = getDistance(necromancerLocation, potentialLocation);

            if (distanceFromSelf >= PHANTOM_PHASE_CONFIG.MIN_DISTANCE_FROM_SELF) {
                // Found a valid location
                validLocation = potentialLocation;
            }
        }

        // If we couldn't find a valid location after max attempts, transition to phase_end
        if (!validLocation) {
            necromancer.triggerEvent("ptd_bb:phantom_phase_end_attack");
            return;
        }

        // Adjust Y level to be as close as possible to target's Y level while ensuring it's an air block
        const adjustedLocation = findNearestAirBlockAtY(necromancer.dimension, validLocation, targetLocation.y);

        if (!adjustedLocation) return;

        // Teleport the necromancer
        await teleport(necromancer, adjustedLocation);

        await system.waitTicks(6);

        // Trigger the phase_end animation
        necromancer.triggerEvent("ptd_bb:phantom_phase_end_attack");
    } catch (error) {
        console.warn(`Error in phantom phase ability: ${error}`);

        // Transition the attack so it won't get stuck in the phantom_phase_start
        // In case it failed to find a valid location
        necromancer.triggerEvent("ptd_bb:phantom_phase_end_attack");
    }
}

/**
 * Finds the nearest air block at or near the specified Y level
 *
 * @param dimension The dimension to check
 * @param location The base location
 * @param targetY The target Y level
 * @returns The adjusted location with the nearest air block Y level, or undefined if none found
 */
function findNearestAirBlockAtY(dimension: Dimension, location: Vector3, targetY: number): Vector3 | undefined {
    // Create a copy of the location
    const adjustedLocation: Vector3 = { ...location };

    // First try the exact Y level
    adjustedLocation.y = targetY;
    if (isAirBlock(dimension, adjustedLocation)) {
        return adjustedLocation;
    }

    // Search up and down for the nearest air block
    const MAX_SEARCH_DISTANCE = 10; // Maximum blocks to search up or down

    for (let offset = 1; offset <= MAX_SEARCH_DISTANCE; offset++) {
        // Try above
        adjustedLocation.y = targetY + offset;
        if (isAirBlock(dimension, adjustedLocation)) {
            return adjustedLocation;
        }

        // Try below
        adjustedLocation.y = targetY - offset;
        if (isAirBlock(dimension, adjustedLocation)) {
            return adjustedLocation;
        }
    }

    // If no air block found, return undefined
    return undefined;
}

/**
 * Checks if the block at the specified location is air
 *
 * @param dimension The dimension to check
 * @param location The location to check
 * @returns True if the block is air, false otherwise
 */
function isAirBlock(dimension: Dimension, location: Vector3): boolean {
    const block = dimension.getBlock(location);
    return block?.isAir ?? false;
}
