import { EntityDamageCause, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../../../general_attacks/attackDamages";
import { cameraShake } from "../../../../general_effects/camerashake";
/**
 * Attack timing constants for left shout attack
 */
const SHOUT_EFFECT_TIMING = 65; // Apply shout effect at tick 65
const ANIMATION_TIME = 130; // Total animation time in ticks
const COOLDOWN_TIME = 20; // Cooldown time in ticks after the attack completes
/**
 * Attack configuration
 */
const ATTACK_CONFIG = {
    /** Range of the shout effect */
    RANGE: 16,
    /** Knockback strength */
    KNOCKBACK_STRENGTH: 2.0,
    /** Camera shake intensity */
    SHAKE_INTENSITY: 0.05,
    /** Number of shockwave rings */
    SHOCKWAVE_RINGS: 4
};
/**
 * Executes the left shout attack for the Void Hydra
 * Creates a powerful shockwave that damages and knocks back all nearby entities
 *
 * @param voidHydra The void hydra entity
 */
export function executeLeftShoutAttack(voidHydra) {
    // Apply shout effect at tick 65
    let shoutEffectTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_bb:dead");
            if (isDead) {
                system.clearRun(shoutEffectTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_bb:attack") === "left_shout") {
                performShoutEffect(voidHydra);
            }
        }
        catch (error) {
            system.clearRun(shoutEffectTiming);
        }
    }, SHOUT_EFFECT_TIMING);
    // Reset attack and start cooldown after animation completes
    let resetTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_bb:dead");
            if (isDead) {
                system.clearRun(resetTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_bb:attack") === "left_shout") {
                voidHydra.triggerEvent("ptd_bb:reset_attack");
            }
        }
        catch (error) {
            system.clearRun(resetTiming);
        }
    }, ANIMATION_TIME);
    // End cooldown after cooldown time
    let cooldownTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_bb:dead");
            if (isDead) {
                system.clearRun(cooldownTiming);
                return;
            }
            voidHydra.setProperty("ptd_bb:cooling_down", false);
        }
        catch (error) {
            system.clearRun(cooldownTiming);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
/**
 * Performs the shout effect with expanding shockwaves
 * @param voidHydra The void hydra entity
 */
function performShoutEffect(voidHydra) {
    try {
        const origin = voidHydra.location;
        const damage = VOID_HYDRA_ATTACK_DAMAGES.left_shout.damage;
        // Apply camera shake to nearby players
        cameraShake(voidHydra, ATTACK_CONFIG.RANGE, ATTACK_CONFIG.SHAKE_INTENSITY, 1.0, 1.0);
        // Get all entities within range
        const entities = voidHydra.dimension.getEntities({
            location: origin,
            maxDistance: ATTACK_CONFIG.RANGE,
            excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
            excludeFamilies: ["void_hydra", "boss"]
        });
        // Apply damage and knockback to all entities
        entities.forEach((entity) => {
            // Apply damage
            entity.applyDamage(damage, {
                cause: EntityDamageCause.entityAttack,
                damagingEntity: voidHydra
            });
            // Calculate knockback direction
            const knockbackDirection = {
                x: entity.location.x - origin.x,
                y: 0,
                z: entity.location.z - origin.z
            };
            // Normalize and apply knockback
            const magnitude = Math.sqrt(knockbackDirection.x ** 2 + knockbackDirection.z ** 2);
            if (magnitude > 0) {
                knockbackDirection.x = (knockbackDirection.x / magnitude) * ATTACK_CONFIG.KNOCKBACK_STRENGTH;
                knockbackDirection.z = (knockbackDirection.z / magnitude) * ATTACK_CONFIG.KNOCKBACK_STRENGTH;
                try {
                    entity.applyKnockback(knockbackDirection.x, knockbackDirection.z, ATTACK_CONFIG.KNOCKBACK_STRENGTH, 0.6);
                }
                catch (knockbackError) {
                    try {
                        const impulse = {
                            x: knockbackDirection.x * 0.8,
                            y: 0.4,
                            z: knockbackDirection.z * 0.8
                        };
                        entity.applyImpulse(impulse);
                    }
                    catch (impulseError) {
                        // Ignore if both methods fail
                    }
                }
            }
        });
        // Create expanding shockwave visual effects
        for (let ring = 0; ring < ATTACK_CONFIG.SHOCKWAVE_RINGS; ring++) {
            const delay = ring * 3; // 3 tick delay between rings
            system.runTimeout(() => {
                try {
                    const isDead = voidHydra.getProperty("ptd_bb:dead");
                    if (isDead)
                        return;
                    createShockwaveRing(voidHydra, origin, (ring + 1) * (ATTACK_CONFIG.RANGE / ATTACK_CONFIG.SHOCKWAVE_RINGS));
                }
                catch (error) {
                    // Handle errors silently
                }
            }, delay);
        }
        // Spawn central explosion effect
        voidHydra.dimension.spawnParticle("minecraft:huge_explosion_emitter", origin);
    }
    catch (error) {
        // Handle errors silently
    }
}
/**
 * Creates a shockwave ring at the specified radius
 * @param voidHydra The void hydra entity
 * @param center The center position of the shockwave
 * @param radius The radius of the shockwave ring
 */
function createShockwaveRing(voidHydra, center, radius) {
    try {
        const particleCount = Math.floor(radius * 6); // More particles for larger rings
        for (let i = 0; i < particleCount; i++) {
            const angle = (i / particleCount) * Math.PI * 2;
            const x = center.x + Math.cos(angle) * radius;
            const z = center.z + Math.sin(angle) * radius;
            const y = center.y + (Math.random() * 2 - 1); // Slight vertical variation
            // Spawn shockwave particles
            voidHydra.dimension.spawnParticle("minecraft:critical_hit_emitter", { x, y, z });
        }
    }
    catch (error) {
        // Handle errors silently
    }
}
