import { Entity, ItemStack, Vector3, system } from "@minecraft/server";

/**
 * Configuration options for the item fountain effect
 */
export interface ItemFountainOptions {
    /**
     * Vertical height offset from the entity's position
     * @default 2.25
     */
    heightOffset?: number;

    /**
     * Minimum horizontal impulse strength
     * @default 0.2
     */
    minHorizontalStrength?: number;

    /**
     * Maximum horizontal impulse strength
     * @default 0.5
     */
    maxHorizontalStrength?: number;

    /**
     * Minimum vertical impulse strength
     * @default 0.3
     */
    minVerticalStrength?: number;

    /**
     * Maximum vertical impulse strength
     * @default 0.7
     */
    maxVerticalStrength?: number;

    /**
     * Delay between item spawns in ticks
     * @default [1, 2] (alternating between 1 and 2 ticks)
     */
    spawnDelay?: number | number[];

    /**
     * Particle effect to spawn with each item
     * If not provided, no particles will be spawned
     */
    particleEffect?: string;

    /**
     * Sound effect to play when the fountain starts
     * If not provided, no sound will be played
     */
    soundEffect?: string;
}

/**
 * Default options for the item fountain effect
 */
const DEFAULT_OPTIONS: ItemFountainOptions = {
    heightOffset: 2.25,
    minHorizontalStrength: 0.2,
    maxHorizontalStrength: 0.5,
    minVerticalStrength: 0.3,
    maxVerticalStrength: 0.7,
    spawnDelay: [1, 2] // Alternating between 1 and 2 ticks
};

/**
 * Spawns items in a fountain-like effect
 *
 * @param entity The entity at whose location to spawn the fountain
 * @param itemId The item identifier to spawn
 * @param count The number of items to spawn
 * @param options Configuration options for the fountain effect
 */
export async function spawnItemFountain(
    entity: Entity,
    itemId: string,
    count: number,
    options?: ItemFountainOptions
): Promise<void> {
    // Merge default options with provided options
    const config = { ...DEFAULT_OPTIONS, ...options };

    const dimension = entity.dimension;
    const location = entity.location;

    // Base spawn location
    const spawnLocation: Vector3 = {
        x: location.x,
        y: location.y + (config.heightOffset || 2.25),
        z: location.z
    };

    // Create a new ItemStack for the item
    const itemStack = new ItemStack(itemId, 1);

    // Play sound effect if provided
    if (config.soundEffect) {
        dimension.playSound(config.soundEffect, location);
    }

    // Spawn items with a delay to create a fountain effect
    for (let i = 0; i < count; i++) {
        // Determine delay for this item
        let delay = 1; // Default delay
        if (Array.isArray(config.spawnDelay)) {
            // Use alternating delays if an array is provided
            const index = i % config.spawnDelay.length;
            const delayValue = config.spawnDelay[index];
            if (typeof delayValue === 'number') {
                delay = delayValue;
            }
        } else if (typeof config.spawnDelay === 'number') {
            // Use fixed delay if a single number is provided
            delay = config.spawnDelay;
        }

        // Wait the specified number of ticks between spawns
        await system.waitTicks(delay);

        try {
            // Spawn the item
            const item = dimension.spawnItem(itemStack, spawnLocation);

            if (item) {
                // Apply random impulse for fountain effect
                applyFountainImpulse(item, config);

                // Spawn particle effect if provided
                if (config.particleEffect) {
                    dimension.spawnParticle(config.particleEffect, item.location);
                }
            }
        } catch (error) {
            console.warn(`Error spawning item in fountain: ${error}`);
        }
    }
}

/**
 * Applies a random impulse to an entity to create a fountain-like effect
 *
 * @param entity The entity to apply impulse to
 * @param options Configuration options for the impulse
 */
function applyFountainImpulse(entity: Entity, options: ItemFountainOptions): void {
    try {
        // Generate random horizontal direction
        const angle = Math.random() * Math.PI * 2; // Random angle in radians

        // Calculate random strengths within the configured ranges
        const minHorizontal = options.minHorizontalStrength || 0.2;
        const maxHorizontal = options.maxHorizontalStrength || 0.5;
        const horizontalStrength = minHorizontal + (Math.random() * (maxHorizontal - minHorizontal));

        const minVertical = options.minVerticalStrength || 0.3;
        const maxVertical = options.maxVerticalStrength || 0.7;
        const verticalStrength = minVertical + (Math.random() * (maxVertical - minVertical));

        // Calculate impulse components
        const impulse: Vector3 = {
            x: Math.cos(angle) * horizontalStrength,
            y: verticalStrength,
            z: Math.sin(angle) * horizontalStrength
        };

        // Apply the impulse
        entity.applyImpulse(impulse);
    } catch (error) {
        console.warn(`Error applying impulse: ${error}`);
    }
}
