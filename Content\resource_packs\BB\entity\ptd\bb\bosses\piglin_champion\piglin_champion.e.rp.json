{"format_version": "1.21.70", "minecraft:client_entity": {"description": {"identifier": "ptd_bb:piglin_champion", "min_engine_version": "1.16.0", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/ptd/bb/entity/bosses/piglin_champion/default"}, "geometry": {"default": "geometry.ptd_bb_piglin_champion"}, "animations": {"particle_effects": "animation.ptd_bb.piglin_champion.particle_effects", "puke_scale_down": "animation.ptd_bb.piglin_champion.puke_scale_down", "look_at_target": "animation.common.look_at_target", "spawn": "animation.ptd_bb.piglin_champion.spawn", "idle": "animation.ptd_bb.piglin_champion.idle", "walk": "animation.ptd_bb.piglin_champion.walk", "death": "animation.ptd_bb.piglin_champion.death", "charge": "animation.ptd_bb.piglin_champion.charge", "charge_3": "animation.ptd_bb.piglin_champion.charge_3", "charge_4": "animation.ptd_bb.piglin_champion.charge_4", "charge_2": "animation.ptd_bb.piglin_champion.charge_2", "charge_1": "animation.ptd_bb.piglin_champion.charge_1", "charging_run": "animation.ptd_bb.piglin_champion.charging_run", "vertical_attack": "animation.ptd_bb.piglin_champion.vertical_attack", "horizontal_attack": "animation.ptd_bb.piglin_champion.horizontal_attack", "healing": "animation.ptd_bb.piglin_champion.healing", "spin_slam": "animation.ptd_bb.piglin_champion.spin_slam", "damage_to_stunned": "animation.ptd_bb.piglin_champion.damaged_to_stunned", "stunned_standing": "animation.ptd_bb.piglin_champion.stunned_standing", "stunned_to_idle": "animation.ptd_bb.piglin_champion.stunned_to_idle", "summoning_chant": "animation.ptd_bb.piglin_champion.summoning_chant", "foot_stomp": "animation.ptd_bb.piglin_champion.foot_stomp", "upchuck": "animation.ptd_bb.piglin_champion.upchuck", "body_slam": "animation.ptd_bb.piglin_champion.body_slam", "stunned_sitting": "animation.ptd_bb.piglin_champion.stunned_sitting", "stunned_sitting_to_idle": "animation.ptd_bb.piglin_champion.stunned_sitting_to_idle", "no_effects": "animation.ptd_bb.piglin_champion.no_effects", "general": "controller.animation.ptd_bb.piglin_champion.general", "stun": "controller.animation.ptd_bb.piglin_champion.stun", "look_at_target_controller": "controller.animation.ptd_bb.piglin_champion.look_at_target"}, "scripts": {"should_update_bones_and_effects_offscreen": true, "animate": ["general", "particle_effects", "look_at_target_controller", {"puke_scale_down": "q.property('ptd_bb:attack') != 'upchuck'"}]}, "render_controllers": ["controller.render.ptd_bb.default"], "particle_effects": {"pg_axe1_01": "ptd_bb:pg_axe1_01", "pg_axe2_01": "ptd_bb:pg_axe2_01", "pg_body_slam1_01": "ptd_bb:pg_body_slam1_01", "pg_burp1_01": "ptd_bb:pg_burp1_01", "pg_burp1_02": "ptd_bb:pg_burp1_02", "pg_burp1_03": "ptd_bb:pg_burp1_03", "pg_charge1_01": "ptd_bb:pg_charge1_01", "pg_charge1_02": "ptd_bb:pg_charge1_02", "pg_charge1_03": "ptd_bb:pg_charge1_03", "pg_die1_01": "ptd_bb:pg_die1_01", "pg_die2_01": "ptd_bb:pg_die2_01", "pg_foot_stomp1_01": "ptd_bb:pg_foot_stomp1_01", "pg_heal1_01": "ptd_bb:pg_heal1_01", "pg_heal2_01": "ptd_bb:pg_heal2_01", "pg_spawn1_01": "ptd_bb:pg_spawn1_01", "pg_spawn2_01": "ptd_bb:pg_spawn2_01", "pg_spinslam1_01": "ptd_bb:pg_spinslam1_01", "pg_spinslam2_01": "ptd_bb:pg_spinslam2_01", "pg_spinslam3_01": "ptd_bb:pg_spinslam3_01", "pg_spinslam4_01": "ptd_bb:pg_spinslam4_01", "pg_summon1_01": "ptd_bb:pg_summon1_01", "pg_summon3_01": "ptd_bb:pg_summon3_01", "pg_upchuck1_01": "ptd_bb:pg_upchuck1_01"}, "sound_effects": {"spawn": "mob.ptd_bb_piglin_champion.spawn", "charging": "mob.ptd_bb_piglin_champion.charging", "vertical_attack": "mob.ptd_bb_piglin_champion.vertical_attack", "horizontal_attack": "mob.ptd_bb_piglin_champion.horizontal_attack", "healing": "mob.ptd_bb_piglin_champion.healing", "spin_slam": "mob.ptd_bb_piglin_champion.spin_slam", "damaged_to_stunned": "mob.ptd_bb_piglin_champion.damaged_to_stunned", "stunned_standing": "mob.ptd_bb_piglin_champion.stunned_standing", "stunned_to_idle": "mob.ptd_bb_piglin_champion.stunned_to_idle", "summoning_chant": "mob.ptd_bb_piglin_champion.summoning_chant", "foot_stomp": "mob.ptd_bb_piglin_champion.foot_stomp", "upchuck": "mob.ptd_bb_piglin_champion.upchuck", "body_slam": "mob.ptd_bb_piglin_champion.body_slam"}, "spawn_egg": {"base_color": "#a57892", "overlay_color": "#9b2794"}}}}