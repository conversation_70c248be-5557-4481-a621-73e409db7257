import { EntityComponentTypes, EntityDamageCause, EquipmentSlot, GameMode, ItemComponentTypes, Player, system } from "@minecraft/server";
import { getDistance } from "../../utilities/vector3";
import { NON_SOLID_BLOCKS } from "../../utilities/constants/nonSolidBlocks";
import { damageItemstack } from "../damageItemstack";
// Global map to track attack timers for players
const playerAttackTimers = new Map();
// Global map to track interval IDs for players
const playerIntervalIds = new Map();
export function piglinChampionAxeOnUse(player, item) {
    try {
        // Apply a 15-second cooldown (300 ticks) to the axe (defined in the item json)
        const cooldownComponent = item.getComponent(ItemComponentTypes.Cooldown);
        if (cooldownComponent) {
            cooldownComponent.startCooldown(player);
        }
        // Check if there's already an attack in progress for this player
        if (!playerAttackTimers.has(player.id)) {
            // Start a new attack sequence
            startVerticalAttack(player);
        }
    }
    catch (error) {
        console.warn(`Error in piglinChampionAxeOnUse: ${error}`);
    }
    return;
}
/**
 * Starts the vertical attack sequence for a player
 * @param player The player who used the axe
 */
async function startVerticalAttack(player) {
    // Initialize attack timer
    playerAttackTimers.set(player.id, 0);
    // Start interval to increment attack timer
    const intervalId = system.runInterval(() => {
        try {
            // Check if player still exists
            try {
                // Access a property to check if player is valid
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                player.id; // Just accessing the property to check if player is valid
            }
            catch (e) {
                cleanupAttack(player.id, intervalId);
                return;
            }
            // Check if player is still holding the axe
            const equippableComponent = player.getComponent(EntityComponentTypes.Equippable);
            if (!equippableComponent) {
                cleanupAttack(player.id, intervalId);
                return;
            }
            const mainHandItem = equippableComponent.getEquipment(EquipmentSlot.Mainhand);
            if (!mainHandItem || mainHandItem.type.id !== 'ptd_bb:piglin_champion_axe') {
                cleanupAttack(player.id, intervalId);
                return;
            }
            // Get current attack timer
            const attackTimer = playerAttackTimers.get(player.id) || 0;
            // Increment attack timer
            playerAttackTimers.set(player.id, attackTimer + 1);
            // Execute attack at tick 19
            if (attackTimer === 19) {
                executePlayerVerticalAttack(player);
            }
            // End attack after 106 ticks (same as the Piglin Champion's vertical attack duration)
            if (attackTimer >= 106) {
                cleanupAttack(player.id, intervalId);
            }
        }
        catch (error) {
            console.warn(`Error in attack interval: ${error}`);
            cleanupAttack(player.id, intervalId);
        }
    });
    // Store interval ID
    playerIntervalIds.set(player.id, intervalId);
}
/**
 * Cleans up the attack resources for a player
 * @param playerId The ID of the player
 * @param intervalId The ID of the interval to clear
 */
function cleanupAttack(playerId, intervalId) {
    // Clear interval
    system.clearRun(intervalId);
    // Remove player from maps
    playerAttackTimers.delete(playerId);
    playerIntervalIds.delete(playerId);
}
/**
 * Finds a position just above the ground (solid block) at the given x,z coordinates
 * @param dimension The dimension to check in
 * @param location The base location to check
 * @param maxSearchDistance Maximum vertical distance to search
 * @returns A location 0.001 blocks above a solid block, or undefined if none found
 */
function findGroundPosition(dimension, location, maxSearchDistance = 20) {
    // Start from a higher position to ensure we find the ground in most cases
    const startY = location.y + 10;
    // Search downward for a solid block
    for (let y = 0; y <= maxSearchDistance * 2; y++) {
        const checkPos = {
            x: location.x,
            y: startY - y,
            z: location.z
        };
        // Get the block at this position
        const block = dimension.getBlock(checkPos);
        // If we found a non-air block
        if (block && !block.isAir) {
            // Check if it's a non-solid block (grass, flower, etc.)
            if (NON_SOLID_BLOCKS.has(block.type.id)) {
                // Instead of returning, continue searching downward
                continue;
            }
            // If it's a solid block, check if the block above is air or non-solid
            const blockAbovePos = {
                x: location.x,
                y: startY - y + 1,
                z: location.z
            };
            // Only return this position if the block above is air or a non-solid block
            if (isAirOrNonSolid(dimension, blockAbovePos)) {
                return {
                    x: location.x,
                    y: startY - y + 1.001, // Position slightly above the solid block
                    z: location.z
                };
            }
            // If the block above isn't air or non-solid, continue searching downward
        }
    }
    // If we couldn't find a suitable ground position, return the original location
    return location;
}
/**
 * Checks if a block at the given location is an air block or a non-solid block
 * If it's a non-solid block, breaks it to simulate physics with particles
 * @param dimension The dimension to check in
 * @param location The location to check
 * @returns True if the block is an air block or a non-solid block, false otherwise
 */
function isAirOrNonSolid(dimension, location) {
    const block = dimension.getBlock(location);
    if (!block)
        return true; // Treat as air if block is null
    if (block.isAir)
        return true;
    // If it's a non-solid block, break it to simulate physics
    if (NON_SOLID_BLOCKS.has(block.type.id)) {
        try {
            // Break the block using the setblock command with destroy flag for particles
            const x = Math.floor(location.x);
            const y = Math.floor(location.y);
            const z = Math.floor(location.z);
            dimension.runCommand(`setblock ${x} ${y} ${z} air [] destroy`);
        }
        catch (error) {
            console.warn(`Error breaking non-solid block with command: ${error}`);
            // Fallback to using setType if the command fails
            try {
                block.setType("minecraft:air");
            }
            catch (fallbackError) {
                console.warn(`Fallback error breaking non-solid block: ${fallbackError}`);
            }
        }
        return true;
    }
    return false;
}
/**
 * Executes the vertical attack for a player
 * @param player The player who used the axe
 */
function executePlayerVerticalAttack(player) {
    const damageRadius = 5;
    const damage = 6; // 6 ticks of health for the axe part
    // Get direction vector directly from the player's view direction
    const viewDirection = player.getViewDirection();
    const dirX = viewDirection.x;
    const dirZ = viewDirection.z;
    // Calculate position 4 blocks in front of the player
    const originPos = {
        x: player.location.x + (dirX * 1),
        y: player.location.y,
        z: player.location.z + (dirZ * 1)
    };
    // Apply damage to nearby entities
    player.dimension.getEntities({
        location: originPos,
        maxDistance: damageRadius,
        excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
        excludeFamilies: ["rock"]
    }).forEach(entity => {
        // Skip the player who used the axe
        if (entity.id === player.id)
            return;
        // Apply damage
        entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: player });
        // Create 2D points (same y-coordinate) to calculate horizontal distance
        const point1 = { x: entity.location.x, y: 0, z: entity.location.z };
        const point2 = { x: originPos.x, y: 0, z: originPos.z };
        const distance = getDistance(point1, point2);
        if (distance > 0) {
            // Use the player's direction for knockback
            const nx = dirX;
            const nz = dirZ;
            // Vertical attack parameters - higher vertical component for the shockwave effect
            const horizontalStrength = 2.0;
            const verticalStrength = 1.5; // Increased to fling entities into the air
            try {
                // Try to apply knockback first
                if (entity instanceof Player) {
                    const gameMode = entity.getGameMode();
                    if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                        entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                    }
                }
                else {
                    entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                }
            }
            catch (e) {
                // Fallback to applyImpulse if applyKnockback fails
                const impulse = {
                    x: nx * horizontalStrength,
                    y: verticalStrength,
                    z: nz * horizontalStrength
                };
                entity.applyImpulse(impulse);
            }
        }
    });
    // Play a sound effect for the attack
    player.dimension.playSound("random.explode", originPos);
    // Spawn rocks sequentially in a straight line
    spawnRocksSequentially(player, originPos, { x: dirX, z: dirZ }, 5);
    // Damage the axe itemstack using the reusable module
    damageItemstack(player, 'ptd_bb:piglin_champion_axe', 10, 20);
}
/**
 * Spawns rocks sequentially in a straight line with a delay between each spawn
 * @param player The player who used the axe
 * @param originPos The origin position for the attack
 * @param direction The direction vector (x and z components)
 * @param rockCount The number of rocks to spawn
 */
async function spawnRocksSequentially(player, originPos, direction, rockCount) {
    // Base distance from origin (0 blocks)
    const baseDistance = 0;
    // Distance between each rock (2 blocks)
    const distanceBetweenRocks = 2;
    // Spawn rocks one by one with a delay
    for (let i = 0; i < rockCount; i++) {
        // Calculate the position for this rock (straight line)
        const distance = baseDistance + (i * distanceBetweenRocks);
        const rockPos = {
            x: originPos.x + (direction.x * distance),
            y: originPos.y,
            z: originPos.z + (direction.z * distance)
        };
        // Find a position just above the ground for spawning the rock
        const spawnPos = findGroundPosition(player.dimension, rockPos);
        // Only spawn the rock if we found a valid position
        if (spawnPos) {
            // Spawn rock entity
            const rock = player.dimension.spawnEntity("ptd_bb:rock", spawnPos);
            if (rock) {
                // Play spawn sound
                player.dimension.playSound("item.trident.throw", spawnPos);
                const rockRadius = 3; // Radius around the rock to check for entities
                player.dimension.getEntities({
                    location: spawnPos,
                    maxDistance: rockRadius,
                    excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
                    excludeFamilies: ["rock"]
                }).forEach(entity => {
                    // Skip the player who used the axe
                    if (entity.id === player.id)
                        return;
                    // Apply the same upward knockback as the initial attack
                    const horizontalStrength = 2.0;
                    const verticalStrength = 1.5;
                    try {
                        // Try to apply knockback first
                        if (entity instanceof Player) {
                            const gameMode = entity.getGameMode();
                            if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                                entity.applyKnockback(direction.x, direction.z, horizontalStrength, verticalStrength);
                            }
                        }
                        else {
                            entity.applyKnockback(direction.x, direction.z, horizontalStrength, verticalStrength);
                        }
                    }
                    catch (e) {
                        // Fallback to applyImpulse if applyKnockback fails
                        const impulse = {
                            x: direction.x * horizontalStrength,
                            y: verticalStrength,
                            z: direction.z * horizontalStrength
                        };
                        entity.applyImpulse(impulse);
                    }
                });
            }
        }
        // Wait 1 tick before spawning the next rock
        await system.waitTicks(1);
    }
}
