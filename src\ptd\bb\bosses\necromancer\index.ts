import { Entity } from "@minecraft/server";
import { selectAttack } from "./controller";
import { getTarget } from "../general_mechanics/targetUtils";

/**
 * Handles the necromancer boss mechanics
 * Currently implements spawning, idle, and movement mechanics
 *
 * @param necromancer The necromancer entity
 */
export function necromancerMechanics(necromancer: Entity): void {
  try {
    // Skip if entity is not valid
    if (!necromancer) return;

    // Handle death mechanics using the generalized function
    // If the entity is dead, this will handle all death-related behavior and return true
    

    // Handle spawning animation
    const isSpawning = necromancer.getProperty("ptd_bb:spawning") as boolean;
    if (isSpawning) {
      return; // Skip other mechanics while spawning
    }

    // Handle attack mechanics using event-driven system
    const attack = necromancer.getProperty("ptd_bb:attack") as string;
    const coolingDown = necromancer.getProperty("ptd_bb:cooling_down") as boolean;

    if (attack && attack !== "none") {
      // Attack is active - execution is handled by individual attack functions using runTimeout
      // No timer increment needed as attacks use event-driven patterns
      return;
    } else if (!coolingDown) {
      // No attack is active and not cooling down, handle attack selection
      const target = getTarget(necromancer, necromancer.location, 32, ["necromancer"]);
      if (target) {
        selectAttack(necromancer, target);
      }
    }
  } catch (error) {
    console.warn(`Error in necromancer mechanics: ${error}`);
  }
}
