import { system } from "@minecraft/server";
import { doGrimhowlRoar, doGrimhowlSlash, doGrimhowlPounce, doGrimhowlShadowOnslaught } from "./attacks/index";
import { grimhowlMeleeRangeAttackHandler } from "./attack_handler";
// Track damage taken by each Grimhowl
export const grimHowlDamageTracker = new Map();
export function grimhowlScriptEventHandler(event) {
    try {
        const sourceEntity = event.sourceEntity;
        switch (event.id) {
            case "ptd_bb:run_melee_range_attack":
                grimhowlMeleeRangeAttackHandler(sourceEntity);
                break;
            case "ptd_bb:run_mid_range_attack":
                doGrimhowlSlash(sourceEntity);
                break;
            case "ptd_bb:run_long_range_attack":
                if (sourceEntity.getProperty('ptd_bb:enraged')) {
                    doGrimhowlShadowOnslaught(sourceEntity);
                }
                else {
                    doGrimhowlPounce(sourceEntity);
                }
                break;
            case "ptd_bb:enrage_toggle":
                toggleEnrageMode(sourceEntity);
                break;
            case "ptd_bb:transition_to_swordless":
                sourceEntity.triggerEvent('ptd_bb:transition_to_swordless');
                break;
            case "ptd_bb:grimhowl_do_the_roar":
                if (sourceEntity.getProperty('ptd_bb:enraged')) {
                    sourceEntity.triggerEvent('ptd_bb:attack_done');
                    doGrimhowlRoar(sourceEntity);
                }
                else {
                    sourceEntity.triggerEvent('ptd_bb:attack_done');
                    sourceEntity.setProperty('ptd_bb:enraged', true);
                    doGrimhowlRoar(sourceEntity);
                }
                break;
            case "ptd_bb:grimhowl_arrows_reset":
                let arrowHits = 0;
                for (let i = 1; i <= 12; i++) {
                    sourceEntity.setProperty(`ptd_bb:arrow_hit_${i}`, false);
                    arrowHits++;
                }
                break;
            default:
                break;
        }
    }
    catch (error) {
        event.sourceEntity.triggerEvent('ptd_bb:attack_done');
    }
}
export function grimhowlHurtHandler(event) {
    try {
        const target = event.hurtEntity;
        if (target && target.typeId === "ptd_bb:grimhowl") {
            const damagingProjectile = event.damageSource.damagingProjectile;
            target.runCommand(`playsound mob.ptd_bb_grimhowl.grunt @a ~ ~ ~ 10 ${0.5 + (Math.random() * 0.3)}`);
            const damage = event.damage;
            // --- Arrow hit logic start ---
            if (damagingProjectile && event.damage >= 4.5) {
                const arrowProps = [];
                for (let i = 1; i <= 12; i++) {
                    arrowProps.push(`ptd_bb:arrow_hit_${i}`);
                }
                const available = arrowProps.filter(prop => !target.getProperty(prop));
                if (available.length > 0) {
                    const propName = available[Math.floor(Math.random() * available.length)];
                    target.setProperty(propName, true);
                }
            }
            // --- Arrow hit logic end ---
            const healthComponent = target.getComponent('minecraft:health');
            const healthPercentage = healthComponent.currentValue / healthComponent.effectiveMax;
            if (healthPercentage <= 0.5 && !target.getProperty('ptd_bb:sword_mode')) {
                target.triggerEvent('ptd_bb:transition_to_sword');
                return;
            }
            handleGrimhowlDamage(target, damage);
        }
    }
    catch (error) {
    }
}
export function grimhowlLoadHandler(event) {
    try {
        const target = event.entity;
        if (target && target.typeId === "ptd_bb:grimhowl") {
            if (target.getProperty('ptd_bb:enraged')) {
                toggleEnrageMode(target);
            }
            if (target.getDynamicProperty('ptd_bb:boss_camera_ongoing')) {
                target.setDynamicProperty('ptd_bb:boss_camera_ongoing', false);
            }
        }
    }
    catch (error) {
    }
}
export function toggleEnrageMode(sourceEntity) {
    try {
        if (sourceEntity.getProperty('ptd_bb:enraged')) {
            sourceEntity.setProperty('ptd_bb:enraged', false);
        }
        else {
            sourceEntity.triggerEvent('ptd_bb:attack_done');
            sourceEntity.setProperty('ptd_bb:enraged', true);
            doGrimhowlRoar(sourceEntity);
        }
    }
    catch (error) {
    }
}
export function handleGrimhowlDamage(target, damage) {
    try {
        const targetId = target.id;
        if (!grimHowlDamageTracker.has(targetId)) {
            grimHowlDamageTracker.set(targetId, 0);
        }
        const totalDamage = (grimHowlDamageTracker.get(targetId) ?? 0) + damage;
        grimHowlDamageTracker.set(targetId, totalDamage);
        if (totalDamage >= 100) {
            let arrowHits = 0;
            for (let i = 1; i <= 12; i++) {
                if (target.getProperty(`ptd_bb:arrow_hit_${i}`))
                    arrowHits++;
            }
            if (arrowHits > 0) {
                target.triggerEvent('ptd_bb:grimhowl_arrow_shake');
                target.runCommand(`playsound mob.ptd_bb_grimhowl.arrow_shake @a ~ ~ ~ 10 ${0.5 + (Math.random() * 0.3)}`);
                return;
            }
            else {
                toggleEnrageMode(target);
            }
            grimHowlDamageTracker.set(targetId, 0);
            if (!target.getProperty('ptd_bb:enraged')) {
                system.runTimeout(() => {
                    if (target) {
                        toggleEnrageMode(target);
                    }
                    else {
                        return;
                    }
                }, 20 * 20);
            }
        }
    }
    catch (error) {
    }
}
