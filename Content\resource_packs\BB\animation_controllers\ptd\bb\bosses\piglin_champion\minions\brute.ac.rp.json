{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_bb.piglin_brute.general": {"states": {"default": {"animations": [{"spawn": "q.property('ptd_bb:spawning') == true"}], "transitions": [{"idling": "q.property('ptd_bb:spawning') == false"}], "blend_transition": 0.3}, "idling": {"animations": [{"idle": "v.random_idle == 0"}, {"idle_2": "v.random_idle == 1"}, {"idle_3": "v.random_idle == 2"}], "on_entry": ["v.random_idle = math.random_integer(0, 2);"], "transitions": [{"moving": "q.ground_speed > 0.3"}, {"vertical_attack": "q.is_delayed_attacking"}, {"dead": "q.property('ptd_bb:dead') == true"}, {"running": "q.ground_speed > 0.3 && q.has_target"}], "blend_transition": 0.3}, "moving": {"animations": ["walk"], "transitions": [{"idling": "q.ground_speed < 0.3"}, {"vertical_attack": "q.property('ptd_bb:attack') == 'vertical'"}, {"horizontal_attack": "q.property('ptd_bb:attack') == 'horizontal'"}, {"dead": "q.property('ptd_bb:dead') == true"}, {"running": "q.ground_speed > 0.3 && q.has_target"}], "blend_transition": 0.3}, "running": {"animations": ["run"], "transitions": [{"idling": "q.ground_speed < 0.3"}, {"vertical_attack": "q.property('ptd_bb:attack') == 'vertical'"}, {"horizontal_attack": "q.property('ptd_bb:attack') == 'horizontal'"}, {"dead": "q.property('ptd_bb:dead') == true"}, {"moving": "q.ground_speed > 0.3 && !q.has_target"}], "blend_transition": 0.3}, "vertical_attack": {"animations": ["vertical_attack"], "transitions": [{"idling": "q.property('ptd_bb:attack') == 'none'"}, {"dead": "q.property('ptd_bb:dead') == true"}], "blend_transition": 0.3}, "horizontal_attack": {"animations": ["horizontal_attack"], "transitions": [{"idling": "q.property('ptd_bb:attack') == 'none'"}, {"dead": "q.property('ptd_bb:dead') == true"}], "blend_transition": 0.3}, "dead": {"animations": ["death"]}}}}}