/**
 * Grimhowl boss attack selection and weight management.
 * This file contains logic for selecting attacks based on weighted probabilities
 * and adjusting weights to ensure varied attack patterns.
 */
import { Entity } from "@minecraft/server";
import { defaultEventWeights, EventWeights } from "./constants";
import { dimensions } from "../../main";

/**
 * Map to store attack weights for each Grimhowl boss instance
 * Key: Entity ID, Value: Attack weights object
 */
const entityEventWeights = new Map<string, EventWeights>();

/**
 * Tracks the last selected attack to prevent repetition
 */
let lastSelectedEvent: string | null = null;

/**
 * Gets the attack weights for a specific Grimhowl boss instance
 * Adjusts backstep weight based on number of arrows stuck in the boss
 *
 * @param entityId - The ID of the Grimhowl boss entity
 * @returns The attack weights for the entity
 */
export function getEntityWeights(entityId: string): EventWeights {
    let entity: Entity | undefined;
    for (const dim of dimensions) {
        entity = dim.getEntities({}).find((e: Entity) => e.id === entityId);
        if (entity) break;
    }
    let backstepWeight = defaultEventWeights.backstep;
    if (entity && entity.typeId === "ptd_bb:grimhowl") {
        // Every arrow stuck on Grimhowl would reduce the backstep weight by 10/12
        let arrowHits = 0;
        for (let i = 1; i <= 12; i++) {
            if (entity.getProperty(`ptd_bb:arrow_hit_${i}`)) arrowHits++;
        }
        backstepWeight = defaultEventWeights.backstep - (10/12) * arrowHits;
    }
    if (!entityEventWeights.has(entityId)) {
        entityEventWeights.set(entityId, { ...defaultEventWeights, backstep: backstepWeight });
    } else {
        const weights = entityEventWeights.get(entityId)!;
        weights.backstep = backstepWeight;
    }
    return entityEventWeights.get(entityId)!;
}

/**
 * Normalizes attack weights to ensure they sum to 1
 * This converts raw weights to probabilities
 *
 * @param weights - The raw attack weights
 * @returns Normalized weights that sum to 1
 */
export function normalizeWeights(weights: EventWeights) {
    const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
    return Object.fromEntries(
        Object.entries(weights).map(([key, weight]) => [key, weight / totalWeight])
    ) as EventWeights;
}

/**
 * Selects an attack based on weighted probabilities
 *
 * @param weights - The attack weights
 * @returns The selected attack name
 */
export function selectEvent(weights: EventWeights): any {
    const normalizedWeights = normalizeWeights(weights);
    const random = Math.random();
    let cumulative = 0;
    for (const [event, weight] of Object.entries(normalizedWeights)) {
        cumulative += weight;
        if (random < cumulative) {
            return event;
        }
    }
    return Object.keys(normalizedWeights)[0];
}

/**
 * Updates attack weights after an attack is selected
 * Reduces the weight of the selected attack and increases others
 * to encourage varied attack patterns
 *
 * @param entityId - The ID of the Grimhowl boss entity
 * @param lastEvent - The last selected attack
 */
export function updateWeights(entityId: string, lastEvent: string) {
    let weights = getEntityWeights(entityId);
    if (lastSelectedEvent !== lastEvent) {
        weights = { ...defaultEventWeights };
    }
    Object.keys(weights).forEach(event => {
        weights[event as keyof EventWeights] += event === lastEvent ? -0.5 : 0.2;
        weights[event as keyof EventWeights] = Math.max(weights[event as keyof EventWeights], 0.1);
    });
    entityEventWeights.set(entityId, weights);
    lastSelectedEvent = lastEvent;
}

/**
 * Resets attack weights for a specific entity or all entities
 *
 * @param entityId - Optional ID of the entity to reset weights for
 *                   If not provided, resets weights for all entities
 */
export function resetEntityWeights(entityId?: string) {
    if (entityId) {
        entityEventWeights.delete(entityId);
    } else {
        entityEventWeights.clear();
    }
    lastSelectedEvent = null;
}
