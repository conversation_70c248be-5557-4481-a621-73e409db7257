/**
 * Handles the backstep move for the Grimhowl entity.
 */

import { system, Entity, Vector3, EntityDamageCause } from "@minecraft/server";
import { filterValidTargets } from "../../general_mechanics/targetUtils";
import { getDirection } from "../../../utilities/vector3";
import { GRIMHOWL_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";

/**
 * Handles the backstep move for the Grimhowl entity.
 * @param {Entity} sourceEntity - The entity performing the attack.
 * @param {Entity} targetEntity - The target entity to backstep against.
 */

export function doGrimhowlBackstep(sourceEntity: Entity, targetEntity: Entity): void {
    try {
        if (!sourceEntity) return;
        
        const isSwordMode: boolean = sourceEntity.getProperty('ptd_bb:sword_mode') as boolean;
        
        sourceEntity.triggerEvent('ptd_bb:grimhowl_backstep');
        const grimHowlLocation: Vector3 = sourceEntity.location;
        const facingLocation: Vector3 = targetEntity.location;
        sourceEntity.teleport(grimHowlLocation, { facingLocation: facingLocation, keepVelocity: true });
        
        system.runTimeout(() => {
            if (!sourceEntity) return;
            const viewDirection: Vector3 = sourceEntity.getViewDirection();
            const impulse: Vector3 = {
                x: viewDirection.x * -2.5,
                y: 0.5,
                z: viewDirection.z * -2.5
            };
            sourceEntity.applyImpulse(impulse);
        
            const nearbyEntities: Entity[] = sourceEntity.dimension.getEntities({
                location: grimHowlLocation,
                excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
                maxDistance: 8
            }).filter(filterValidTargets(sourceEntity));
        
            nearbyEntities.forEach((entity: Entity) => {
                const direction: Vector3 = getDirection( grimHowlLocation, entity.location);
            
                const magnitude: number = Math.sqrt(direction.x ** 2 + direction.y ** 2 + direction.z ** 2);
                const normalizedDirection: Vector3 = magnitude === 0
                    ? { x: 0, y: 0, z: 0 }
                    : {
                        x: direction.x / magnitude,
                        y: direction.y / magnitude,
                        z: direction.z / magnitude
                    };
                
                const isSneaking: boolean = entity.isSneaking;
                
                if (isSwordMode) {
                    sourceEntity.runCommand(`playsound mob.ptd_bb_grimhowl.sword_swipe @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
                    if (isSneaking) {
                        entity.applyKnockback(normalizedDirection.x, normalizedDirection.z, 1.25, 0.25);
                    } else {
                        entity.applyKnockback(normalizedDirection.x, normalizedDirection.z, 2.5, 0.5);
                    }
                    entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.backstep_sword.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
                } else {
                    if (isSneaking) {
                        entity.applyKnockback(normalizedDirection.x, normalizedDirection.z, 0.5, 0.25);
                    } else {
                        entity.applyKnockback(normalizedDirection.x, normalizedDirection.z, 1, 0.5);
                    }
                }
            });
        }, 20 * 0.38);
    } catch (error) {
        sourceEntity?.triggerEvent('ptd_bb:attack_done');
    }
}