/**
 * Grimhowl boss left claw attack implementation.
 * This file contains the logic for executing the left claw attack,
 * including animation, sound effects, and damage application.
 */
import { system, Entity, EntityDamageCause } from "@minecraft/server";
import { isEntityInFront } from "../../general_mechanics/targetUtils";
import { GRIMHOWL_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";

/**
 * Executes the left claw attack for the Grimhowl boss
 * The attack faces the boss toward a target, plays an animation,
 * and damages entities in front of the boss
 *
 * @param sourceEntity - The Grimhowl boss entity
 * @param frontTargets - Array of entities in front of the boss
 */

export function doGrimhowlRightClaw(sourceEntity: Entity, nearbyTargets: Entity[]): void {
    try {
        if (!sourceEntity) return;

        const isEnraged: boolean = sourceEntity.getProperty('ptd_bb:enraged') as boolean;
        const isSwordMode: boolean = sourceEntity.getProperty('ptd_bb:sword_mode') as boolean;

        if (isEnraged) {
            sourceEntity.triggerEvent('ptd_bb:grimhowl_claw_right_enraged');
        } else {
            sourceEntity.triggerEvent('ptd_bb:grimhowl_claw_right');
        }

        // Check if sourceEntity is still valid before teleporting
        if (!sourceEntity) return;

        // Check if a frontTarget to face to, if not, face defaultFacingLocation
        sourceEntity.runCommand(`playsound mob.ptd_bb_grimhowl.grunt @a ~ ~ ~ 5${1 + (Math.random() * 0.3)}`);

        system.runTimeout(() => {
            if (!sourceEntity) return;
            const updatedFrontTargets: Entity[] = nearbyTargets.filter((entity: Entity) => {
                return isEntityInFront(sourceEntity, entity, 0.75);
            });

            updatedFrontTargets.forEach((entity: Entity) => {
                entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.left_claw.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
                entity.applyKnockback(
                    sourceEntity.getViewDirection().x,
                    sourceEntity.getViewDirection().z,
                    isSwordMode ? 2.5 : -2.5,
                    0.25
                );
            });
        }, isEnraged ? 9 : 18);

        system.runTimeout(() => {
            if (!sourceEntity) return;
            sourceEntity.runCommand(`playsound mob.ptd_bb_grimhowl.claw_attack @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
        }, isEnraged ? 0.75 * 10 : 0.75 * 20);

        system.runTimeout(() => {
            if (!sourceEntity) return;
            sourceEntity.runCommand(`playsound mob.ptd_bb_grimhowl.pre_claw @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
        }, isEnraged ? 0.75 * 10 : 0.75 * 20);
    } catch (error) {
        sourceEntity?.triggerEvent('ptd_bb:attack_done');
    }
}