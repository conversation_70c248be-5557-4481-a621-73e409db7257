{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_bb.piglin_champion.look_at_target": {"initial_state": "not_looking", "states": {"not_looking": {"transitions": [{"looking": "q.property('ptd_bb:spawning') == false && q.property('ptd_bb:dead') == false && q.property('ptd_bb:attack') != 'charging' && q.property('ptd_bb:attack') != 'stunned_standing' && q.property('ptd_bb:attack') != 'stunned_sitting'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "looking": {"animations": ["look_at_target"], "transitions": [{"not_looking": "q.property('ptd_bb:spawning') == true || q.property('ptd_bb:dead') == true || q.property('ptd_bb:attack') == 'charging' || q.property('ptd_bb:attack') == 'stunned_standing' || q.property('ptd_bb:attack') == 'stunned_sitting'"}], "blend_transition": 0.3, "blend_via_shortest_path": true}}}}}