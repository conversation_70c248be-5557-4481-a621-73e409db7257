/**
 * Main entry point for Grimhowl boss mechanics.
 * This file handles the core event dispatching and death mechanics for the Grimhowl boss.
 */
import { grimhowl<PERSON><PERSON><PERSON><PERSON><PERSON>, grimhowl<PERSON>oad<PERSON><PERSON><PERSON>, grimhowlScriptEventHandler } from "./entity_ai";
import { handleDeathMechanics } from "../general_mechanics/deathMechanics";
import { stopGrimhowlSounds } from "./soundManager";
import { spawnItemFountain } from "../general_mechanics/itemFountain";
import { showcaseBossWithCinematicCamera } from "../general_mechanics/cinematicCamera";

/**
 * Standard damage value for light attacks
 */
export const lightDamage = 4;

/**
 * Standard damage value for heavy attacks
 */
export const heavyDamage = 8;

/**
 * Unified dispatcher for Grimhowl boss events
 * Handles different event types and routes them to appropriate handlers
 *
 * @param event - The event object containing event data
 * @param type - The type of event to handle (hurt, load, script, death)
 */
export function grimhowlMechanics(event: any, type: "hurt" | "load" | "script" | "death") {
    try {
        switch (type) {
          case "hurt":
            grimhowl<PERSON><PERSON><PERSON><PERSON><PERSON>(event);
            break;
          case "load":
            grimhowl<PERSON>oadHandler(event);
            break;
          case "script":
            grimhowlScriptEventHandler(event);
            break;
          case "death":
            const isDead = event.getProperty("ptd_bb:dead");
                  if (isDead) {
                      if (!event.getDynamicProperty('ptd_bb:boss_camera_ongoing')) {
                          showcaseBossWithCinematicCamera(event, {title:"GRIMHOWL", subtitle:"VANQUISHED!", closeUpTimePercentage:0.3, keyframeCount:2, totalDuration:7, semiCloseUpDistance:9, closeUpDistance:11, radius:8});
                      }
                      event.getComponent('minecraft:health').setCurrentValue(0.01);
                  };
                
            if (
              handleDeathMechanics(event, {
                // Configure death mechanics specific to the Piglin Champion
                duration: 100,
                xpOrbs: {
                  count: 8,
                  duration: 30,
                  heightOffset: 2.25
                },
                // No drops here as we'll use a custom event to spawn the essence fountain
                drops: [],
                deathSound: "mob.ptd_bb_grimhowl.roar",
                // Add custom event to spawn essence fountain at the beginning of death sequence
                customEvents: [
                  {
                    tick: 1,
                    callback: (entity) => {
                      // Spawn 32 essence items in a fountain-like effect
                      spawnItemFountain(entity, "ptd_bb:grim_howl_essence", 32, {
                        heightOffset: 2.25,
                        particleEffect: "minecraft:large_explosion",
                        soundEffect: "random.pop",
                        minVerticalStrength: 0.1,
                        maxVerticalStrength: 0.3,
                        minHorizontalStrength: 0.05,
                        maxHorizontalStrength: 0.2
                      });
                    }
                  }
                ],
                // Provide the sound stopping function
                stopSoundsFn: stopGrimhowlSounds
              })
            ) {
              return;
            }
        }
    } catch (error) {
    }
}
